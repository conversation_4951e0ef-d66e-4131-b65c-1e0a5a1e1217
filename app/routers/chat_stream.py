from fastapi import APIRouter, Request
from fastapi.responses import StreamingResponse
from typing import AsyncGenerator, List, Dict
import json

from flask import session
from loguru import logger
from app.services import RecallService
from app.services.medical_record import summarize_last_two_rounds, check_medical_record_completeness,generate_syndrome_analysis,generate_prescription_analysis
import asyncio
from app.core import get_model, get_big_model, get_small_model
from uuid import uuid4
from langfuse import get_client, observe,Langfuse
from langfuse.langchain import CallbackHandler
router = APIRouter()
from langchain_deepseek import ChatDeepSeek
from langchain_core.prompts import ChatPromptTemplate
from app.services.recall import label_extend


async def handle_normal_chat(messages: List[Dict[str, str]], books: List, langfuse_handler: CallbackHandler, trace_id: str, root_span):
    """
    处理普通对话 (type=1)
    """

    # 多轮对话总结
    user_messages = [msg for msg in messages if msg.get("role") == "user"]
    if len(user_messages) >= 2:
        # 如果有多轮对话，使用总结内容进行召回
        query_content = await summarize_last_two_rounds(messages, type=1)
    else:
        # 如果只有一轮对话，直接使用最后一条用户消息
        query_content = user_messages[-1].get("content", "")

    recall_service = RecallService()
    recall_result = await recall_service.recall_filter(langfuse_handler=langfuse_handler, query=query_content, books=books)

    recall = recall_result['recall']
    recall = list(enumerate(recall, 1))

    root_span.update_trace(
        name="chat/stream",
        tags=["recall"],
        metadata={"recall": recall}
    )
    
    # 获取最近三轮对话
    history = []
    current_round = 0
    for msg in reversed(messages[:-1]):  # 不包括当前这一轮的最新消息
        if current_round >= 2:  # 只取最近三轮
            break
        if msg["role"] == "user":
            history.insert(0, {"role": "user", "content": msg["content"]})
            current_round += 1
        elif msg["role"] == "assistant":  # 确保有对应的用户消息
            history.insert(1, {"role": "assistant", "content": msg["content"]})
            
    # 获取当前轮次的用户消息
    current_query = messages[-1]["content"] if messages else ""
            
    gen_prompt = '''你的名字是轩岐问对，擅长回答与医学相关的问题
                    这是用户的问题：{prompt}
                    这是参考的知识库内容：{recall}
                    这些知识库是一个个的片段，可能有助于回答问题，也可能对回答问题没有帮助，可能需要整合后才能回答问题，也可能需要经过一定的推理才能回答问题
                    现在要求你：
                    1.从知识库片段中推理出答案，需要一步步地进行思考、推理；
                    2.进行结构化回答，第一部分是知识库整合分析过程并推理，第二部分是最终回答。以下是你的回答示例：
                    ### 【知识库整合分析】
                    具体整合内容，越详细越好。阐述的时候必须引用参考知识库，并在句尾标注索引号。如:知识库整合内容[0][1]，知识库条文的顺序从1开始。
                    ### 【最终回答】
                    基于分析的结论性答复，如无相关知识库则直接回答已知信息 
                    3.对于参考价值不大的知识片段，不要纳入思考和推理的范围里。
                    4.推理逻辑越严密越好。
                    5.如果参考知识条文为空列表，则直接回答问题，不要胡编乱造。
                    6.索引标注规范：
                       - 所有引用**必须**使用方括号标注，如：[1]
                       - 索引编号从1开始（即列表首条为[1]）
                       - 禁止使用其他格式：（片段1）、(索引1)、[1,2]、（知识库1、2）等格式
                    请现在给出你的回答：
                   '''
                   
    input_data = {
        "history": "\n".join([f"{msg['role']}: {msg['content']}" for msg in history]),
        "query": current_query,
        "recall":  recall
    }
    
    return recall_result['output_all'], gen_prompt, input_data

async def handle_medical_chat(
                            type: int,
                            base_info: str, syndrome: str, prescription: str,
                            langfuse_handler: CallbackHandler, root_span):
    """
    处理医疗相关对话 (type=2 病历分析, type=3 药方分析)
    """
    # 获取最后一条用户消息
    if type == 2:
        result = await generate_syndrome_analysis(root_span,base_info, syndrome, langfuse_handler)
        return result
    elif type == 3:
        result = await generate_prescription_analysis(root_span, base_info, prescription, langfuse_handler)
        return result
    else:
        return None



@router.post("/chat/stream")
async def chat_stream(request: Request):
    """
    处理流式对话请求的路由

    Args:
        request: FastAPI请求对象，请求体格式为：
            {
                "message": [
                    {"role": "user", "content": "用户消息"},
                    {"role": "assistant", "content": "助手回复"}
                ],
                "books": [],
                "type": 1,  # 1:普通对话 2:病历分析 3:药方分析
                "is_complete": 0,  # 仅用于医疗相关对话
                "base_info": "",  # 仅用于医疗相关对话
                "syndrome": "",   # 仅用于医疗相关对话
                "prescription": "" # 仅用于医疗相关对话
            }

    Returns:
        StreamingResponse: 流式响应对象
    """
    try:
        langfuse = Langfuse()
        trace_id = str(uuid4()).replace('-', '').lower()
        langfuse_handler = CallbackHandler()
        with langfuse.start_as_current_span(name="chat/stream", trace_context={"trace_id": trace_id}) as root_span:

            # 获取请求体数据
            data = await request.json()
            messages = data.get("message", [])
            books = data.get("books", [])
            is_complete = data.get("is_complete", 0)
            # 类型 1:普通对话 2.病历分析 3.药方分析
            type = data.get("type", 1)
            base_info = data.get("base_info", "")
            syndrome = data.get("syndrome", "")
            prescription = data.get("prescription", "")

            if len(messages) == 0:
                is_complete = 0

            root_span.update_trace(
                name="chat/stream",
                tags=["chat"],
                input={"type": type, "content": messages, "books": books, "is_complete": is_complete,
                      "base_info": base_info, "syndrome": syndrome, "prescription": prescription}
            )

            # 根据类型调用不同的处理方法
            if type == 1 or len(messages) > 1:
                output_all,prompt_template,input_data = await handle_normal_chat(
                    messages, books, langfuse_handler, trace_id, root_span
                )
            else:
                output_all,prompt_template,input_data = await handle_medical_chat(
                    type,base_info,syndrome,prescription,langfuse_handler,root_span
                )

            return StreamingResponse(
                ds_stream( output_all,prompt_template,input_data, trace_id),
                media_type="text/event-stream"
            )

    except Exception as e:
        logger.error(f"处理请求时发生错误: {str(e)}")
        return StreamingResponse(
            "error",
            media_type="text/event-stream"
        )




@router.post("/chat/check_medical_record")
async def check_medical_record(request: Request):
    # 类型 2.病历分析 3.药方分析
    # 获取请求体数据
    langfuse = Langfuse()
    trace_id = str(uuid4()).replace('-', '').lower()
    langfuse_handler = CallbackHandler()
    with langfuse.start_as_current_span(name="chat/check_medical_record", trace_context={"trace_id": trace_id}) as root_span:
        data = await request.json()
        type = data.get("type", 2)
        base_info = data.get("base_info", "")
        syndrome = data.get("syndrome", "")
        prescription = data.get("prescription", "")
        root_span.update_trace(
            name="chat/check_medical_record",
            tags=["check"],
            input={"base_info": base_info, "syndrome": syndrome, "prescription": prescription, "type": type}
        )
        result = await check_medical_record_completeness(type, base_info, syndrome, prescription,langfuse_handler)
        root_span.update_trace(
            name="chat/check_medical_record",
            tags=["check"],
            output={"result": result}
        )
    return {"status": 1, 'messages': '请求成功', 'data': result}


@router.post("/chat/node_extend")
async def node_extend(request: Request):
    """
    获取节点关系

    Args:
        request: FastAPI请求对象

    Returns:
        StreamingResponse: 流式响应对象
    """
    try:
        # 获取请求体数据
        data = await request.json()
        label= data.get("label", "")

        node_list, relation_list = label_extend(label=label)

        # recall = recall_result['recall']
        # recall = list(enumerate(recall, 1))

        return  {"status": 1, 'messages': '请求成功','data':{"node_list":node_list, "relation_list":relation_list}}

    except Exception as e:
        logger.error(f"处理请求时发生错误: {str(e)}")
        return {"status": 1, 'messages': '请求成功'}

async def format_sse_event(data: str,type: str) -> str:
    """
    格式化服务器发送事件(SSE)消息

    Args:
        data: 要发送的数据

    Returns:
        str: 格式化后的SSE消息
    """
    return f"data: {json.dumps({'type':type ,'content': data}, ensure_ascii=False)}\n\n"

async def ds_stream(output_all: list,
                    prompt_template: str,
                    input_data: dict,
                    trace_id: str) -> AsyncGenerator[str, None]:
    """
    处理流式对话并生成响应

    Args:
        messages: 对话历史列表，每个元素包含role和content
        recall: 召回的知识库内容
        output_all: 完整的召回列表
        prompt_template: 提示词模板
        input_data: 提示词模板中需要填充的变量
        trace_id: 追踪ID

    Yields:
        str: 流式响应的内容
    """
    langfuse = Langfuse()
    langfuse_handler = CallbackHandler()
    with langfuse.start_as_current_span(name="chat/stream", trace_context={"trace_id": trace_id}) as root_span:

        m = get_big_model()
        prompt = ChatPromptTemplate.from_messages([
            ("user", prompt_template)
        ])

        chain = prompt | m

        try:
            # 使用input_data作为模板变量
            async for chunk in chain.astream(input_data, config={"callbacks": [langfuse_handler]}):
                logger.info(f"流式输出返回：{chunk}")
                if hasattr(chunk, 'content'):
                    # 如果有思考过程，先输出思考过程
                    if hasattr(chunk, 'additional_kwargs') and 'reasoning_content' in chunk.additional_kwargs:
                        reasoning = chunk.additional_kwargs.get('reasoning_content')
                        if reasoning:
                            yield await format_sse_event(reasoning, 'think')
                    # 然后输出正文内容
                    if chunk.content:
                        yield await format_sse_event(chunk.content, 'text')
                else:
                    yield await format_sse_event(str(chunk), 'text')

            yield await format_sse_event(json.dumps(output_all), 'recall_list')
            root_span.update_trace(
                name="chat/stream",
                tags=["chat"],
                output={"content": "结束"}
            )
        except Exception as e:
            logger.error(f"生成回答时发生错误: {str(e)}")
            yield await format_sse_event("生成回答时发生错误", 'text')
