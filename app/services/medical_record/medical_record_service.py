from typing import List, Dict
import json
from langchain_core.prompts import ChatPromptTemplate
from app.core import get_model, get_big_model, get_small_model
from app.services.recall.retrieval_utils import fix_json
from loguru import logger
from langfuse.langchain import CallbackHandler

async def summarize_last_two_rounds(messages: List[Dict[str, str]], type: int) -> str:
    """
    总结对话内容
    
    Args:
        messages: 对话历史列表
        type: 对话类别，当type为2或3时总结全部对话，其他情况只总结最后两轮
        
    Returns:
        str: 总结后的内容
    """
    # 获取模型实例
    m = get_small_model()
    
    # 根据type决定是否总结全部对话
    if type in [2, 3]:
        # 提取所有用户对话
        user_messages = [msg.get("content", "") for msg in messages if msg.get("role") == "user"]
        if not user_messages:
            return ""
            
        # 构建提示词
        summary_prompt = '''
        这是用户的全部对话内容：
        {all_messages}
        
        请总结这些对话的核心内容，使其成为一个连贯的查询语句。总结要求：
        1. 保留关键的医学术语和症状描述
        2. 合并相关的信息
        3. 去除重复的内容
        4. 总结后的内容应该简洁但完整
        
        请直接给出总结内容，不要包含任何额外的解释。
        '''
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", "你是一个中医对话总结专家"),
            ("user", summary_prompt)
        ])

        chain = prompt | m

        # 获取总结结果
        result = await chain.ainvoke({
            "all_messages": "\n".join([f"- {msg}" for msg in reversed(user_messages)])
        })
        
    else:
        # 提取最后两轮用户对话
        user_messages = []
        for msg in reversed(messages):
            if msg.get("role") == "user":
                user_messages.append(msg.get("content", ""))
            if len(user_messages) >= 2:
                break
                
        if len(user_messages) < 2:
            return user_messages[0] if user_messages else ""
            
        # 构建提示词
        summary_prompt = '''
        这是用户最近的两轮对话：
        第一轮：{first_message}
        第二轮：{second_message}
        
        请总结这两轮对话的核心内容，使其成为一个连贯的查询语句。总结要求：
        1. 保留关键的医学术语和症状描述
        2. 合并相关的信息
        3. 去除重复的内容
        4. 总结后的内容应该简洁但完整
        
        请直接给出总结内容，不要包含任何额外的解释。
        '''
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", "你是一个中医对话总结专家"),
            ("user", summary_prompt)
        ])

        chain = prompt | m

        # 获取总结结果
        result = await chain.ainvoke({
            "first_message": user_messages[1],
            "second_message": user_messages[0]
        })
    
    return result.content

async def check_medical_record_completeness(type:int,base_info:str,syndrome:str,prescription:str,langfuse_handler:CallbackHandler) -> Dict:
    """
    使用大模型判断病历或药方的完整性

    Args:
        type: 2为病历分析，3为药方分析
        base_info: 基本信息
        syndrome: 症候信息
        prescription: 处方信息

    Returns:
        Dict: 包含完整性判断结果和缺失信息
    """
    m = get_model()
    result = {
        "is_complete": 1,
        "suggestions": []
    }

    if type == 2:
        # 病历分析模式
        if base_info:
            base_check_prompt = '''
            作为一个中医病历完整性检查专家，判断本次病案解析是否信息充足：
            {content}
            
            请根据以下标准进行判断：
            1. 本次医案学习内容是否属于中医疾病诊疗范畴
            2. 本次医案学习是否包含刻下症状描述
             
            判断逻辑：
            - 若以上两点均满足，表示信息充足，可进行证候逆向推导病机与辨证思路分析
            - 若任一点不满足，表示信息不足，无法完成有效的病案逆向推导
            
            请按以下格式输出：
            {{
                "is_complete": 0,  // 0 不完整 1 完整
                "suggestions": ""  // 具体建议
            }}
            
            直接返回JSON格式数据，不要包含其他说明文字。
            '''

            prompt = ChatPromptTemplate.from_messages([
                ("system", "你是一个中医专家"),
                ("user", base_check_prompt)
            ])
            chain = prompt | m
            base_result = await chain.ainvoke({"content": base_info},config={"callbacks": [langfuse_handler]})
            base_check = fix_json(base_result.content)

            if base_check["is_complete"] == 0:
                result["is_complete"] = 0
                result["suggestions"].append(base_check["suggestions"])

        elif syndrome:
            syndrome_check_prompt = '''
            作为一个中医病历完整性检查专家，判断本次病案解析是否信息充足：
            {content}
            
            请根据以下标准进行判断：
            1. 本次医案学习是否包含至少提供以下一项关键辨证要素：
                 * 明确的基础病机（如"表束、表寒"）
                 * 或清晰的六经证候归属（如"太阴少阴阳明合病"）
             
            判断逻辑：
            - 若满足，表示信息充足，可进行证候逆向推导病机与辨证思路分析
            - 若不满足，表示信息不足，无法完成有效的病案逆向推导
            
            请按以下格式输出：
            {{
                "is_complete": 0,  // 0 不完整 1 完整
                "suggestions": ""  // 具体建议
            }}
            
            直接返回JSON格式数据，不要包含其他说明文字。
            '''

            prompt = ChatPromptTemplate.from_messages([
                ("system", "你是一个中医专家"),
                ("user", syndrome_check_prompt)
            ])
            chain = prompt | m
            syndrome_result = await chain.ainvoke({"content": syndrome},config={"callbacks": [langfuse_handler]})
            syndrome_check = fix_json(syndrome_result.content)

            if syndrome_check["is_complete"] == 0:
                result["is_complete"] = 0
                result["suggestions"].append(syndrome_check["suggestions"])

    elif type == 3:
        # 药方分析模式
        if base_info:
            base_check_prompt = '''
            作为一个中医病历完整性检查专家，判断本次病案解析是否信息充足：
            {content}
            
            请根据以下标准进行判断：
            1. 本次方药分析内容是否属于中医疾病诊疗范畴
            2. 本次方药分析是否包含清晰具体的刻下症状描述
             
            判断逻辑：
            - 若以上两点均满足，表示信息充足，可进行证候逆向推导病机与辨证思路分析
            - 若任一点不满足，表示信息不足，无法完成有效的病案逆向推导
            
            请按以下格式输出：
            {{
                "is_complete": 0,  // 0 不完整 1 完整
                "suggestions": ""  // 具体建议
            }}
            
            直接返回JSON格式数据，不要包含其他说明文字。
            '''

            prompt = ChatPromptTemplate.from_messages([
                ("system", "你是一个中医专家"),
                ("user", base_check_prompt)
            ])
            chain = prompt | m
            base_result = await chain.ainvoke({"content": base_info},config={"callbacks": [langfuse_handler]})
            base_check = fix_json(base_result.content)

            if base_check["is_complete"] == 0:
                result["is_complete"] = 0
                result["suggestions"].append(base_check["suggestions"])

        if prescription:
            prescription_check_prompt = '''
            作为一个中医病历完整性检查专家，判断本次病案解析是否信息充足：
            {content}
            
            请根据以下标准进行判断：
            1. 本次方药分析是否包含：完整处方信息（包括药物组成、剂量等）
            
            判断逻辑：
            - 若满足，表示信息充足，可进行从症状和处方逆向推导病机与辨证思路分析
            - 若不满足，表示信息不足，无法完成有效的病案逆向推导
            
            请按以下格式输出：
            {{
                "is_complete": 0,  // 0 不完整 1 完整
                "suggestions": ""  // 具体建议
            }}
            
            直接返回JSON格式数据，不要包含其他说明文字
            '''

            prompt = ChatPromptTemplate.from_messages([
                ("system", "你是一个中医专家"),
                ("user", prescription_check_prompt)
            ])
            chain = prompt | m
            prescription_result = await chain.ainvoke({"content": prescription},config={"callbacks": [langfuse_handler]})
            prescription_check = fix_json(prescription_result.content)

            if prescription_check["is_complete"] == 0:
                result["is_complete"] = 0
                result["suggestions"].append(prescription_check["suggestions"])

    # 如果没有任何有效输入，返回错误提示
    if type == 2 and not (base_info or syndrome):
        result["is_complete"] = 0
        result["suggestions"].append("请至少提供基本信息或症候信息")
    elif type == 3 and not (base_info or prescription):
        result["is_complete"] = 0
        result["suggestions"].append("请至少提供基本信息或处方信息")

    return {
        "is_complete": result["is_complete"],
        "suggestions": "；".join(result["suggestions"]) if result["suggestions"] else ""
    }
    # return {
    #     "is_complete": 1,
    #     "suggestions":""
    # }